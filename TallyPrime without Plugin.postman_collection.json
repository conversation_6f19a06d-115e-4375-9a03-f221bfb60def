{"info": {"_postman_id": "33b3ea7a-1c21-4f17-b408-0c89dbb22fe7", "name": "TallyPrime without Plugin", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "25350607"}, "item": [{"name": "BalanceSheet", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Balance Sheet</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Sales Voucher", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Sales Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Sales Order Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Sales Order Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Delivery Note Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Delivery Note Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Purchase Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Purchase Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Purchase Order Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Purchase Order Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Receipt Note Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Receipt Note Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Debit Note Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Debit Note Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Credit Note Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Credit Note Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Receipt Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Receipt Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Payment Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Payment Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Contra Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Contra Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Journal Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Journal Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Stock Journal Vouchers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>Stock Journal Vouchers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n\t<STATICVARIABLES>\r\n      \t\t<EXPLODEFLAG>Yes</EXPLODEFLAG>\r\n       \t\t   <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>\r\n\t</STATICVARIABLES>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n\r\n\t"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "List of Ledgers", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n \t<HEADER>\r\n    \t\t<VERSION>1</VERSION>\r\n    \t\t<TALLYREQUEST>Export</TALLYREQUEST>\r\n    \t\t<TYPE>Data</TYPE>\r\n    \t\t<ID>List of Ledgers</ID>\r\n \t</HEADER>\r\n<BODY>\r\n<DESC>\r\n<TDL>\r\n<TDLMESSAGE>\r\n<REPORT NAME=\"List of Ledgers\" ISMODIFY=\"No\" ISFIXED=\"No\" ISINITIALIZE=\"No\" ISOPTION=\"No\" ISINTERNAL=\"No\">\r\n  <FORMS>List of Ledgers</FORMS> \r\n  </REPORT>\r\n<FORM NAME=\"List of Ledgers\" ISMODIFY=\"No\" ISFIXED=\"No\" ISINITIALIZE=\"No\" ISOPTION=\"No\" ISINTERNAL=\"No\">\r\n  <TOPPARTS>List of Ledgers</TOPPARTS> \r\n  <XMLTAG>\"List of Ledgers\"</XMLTAG> \r\n  </FORM>\r\n<PART NAME=\"List of Ledgers\" ISMODIFY=\"No\" ISFIXED=\"No\" ISINITIALIZE=\"No\" ISOPTION=\"No\" ISINTERNAL=\"No\">\r\n  <TOPLINES>List of Ledgers</TOPLINES> \r\n  <REPEAT>List of Ledgers : Collection of Ledgers</REPEAT> \r\n  <SCROLLED>Vertical</SCROLLED> \r\n  </PART>\r\n<LINE NAME=\"List of Ledgers\" ISMODIFY=\"No\" ISFIXED=\"No\" ISINITIALIZE=\"No\" ISOPTION=\"No\" ISINTERNAL=\"No\">\r\n  <LEFTFIELDS>List of Ledgers</LEFTFIELDS> \r\n  </LINE>\r\n<FIELD NAME=\"List of Ledgers\" ISMODIFY=\"No\" ISFIXED=\"No\" ISINITIALIZE=\"No\" ISOPTION=\"No\" ISINTERNAL=\"No\">\r\n  <SET>$Name</SET> \r\n  <XMLTAG>\"NAME\"</XMLTAG> \r\n  </FIELD>\r\n<COLLECTION NAME=\"Collection of Ledgers\" ISMODIFY=\"No\" ISFIXED=\"No\" ISINITIALIZE=\"No\" ISOPTION=\"No\" ISINTERNAL=\"No\">\r\n  <TYPE>Ledger</TYPE> \r\n  </COLLECTION>\r\n  </TDLMESSAGE>\r\n  </TDL>\r\n\r\n</DESC>\r\n</BODY>\r\n</ENVELOPE>\r\n"}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Create Sales Voucher", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n  <HEADER>\r\n    <VERSION>1</VERSION>\r\n    <TALLYREQUEST>Import</TALLYREQUEST>\r\n    <TYPE>Data</TYPE>\r\n    <ID>Vouchers</ID>\r\n  </HEADER>\r\n  <BODY>\r\n    <DESC>\r\n      <STATICVARIABLES>\r\n        <SVCURRENTCOMPANY>Resonate Systems Private Limited</SVCURRENTCOMPANY>\r\n        <IMPORTDUPS>@@DUPCOMBINE</IMPORTDUPS>\r\n      </STATICVARIABLES>\r\n    </DESC>\r\n    <DATA>\r\n      <TALLYMESSAGE xmlns:UDF=\"TallyUDF\">\r\n        <VOUCHER VCHTYPE=\"Sales\" ACTION=\"Create\" OBJVIEW=\"Invoice Voucher View\">\r\n          <DATE>20250725</DATE>\r\n          <VOUCHERTYPENAME>Sales</VOUCHERTYPENAME>\r\n          <VOUCHERNUMBER>RSNT2602</VOUCHERNUMBER>\r\n          <PARTYLEDGERNAME>INGRAM MICRO INDIA PRIVATE LIMITED&#13;&#10;&#13;&#10;</PARTYLEDGERNAME>\r\n          <BASICBUYERNAME>INGRAM MICRO INDIA PRIVATE LIMITED&#13;&#10;&#13;&#10;</BASICBUYERNAME>\r\n          <BASICBASEPARTYNAME>INGRAM MICRO INDIA PRIVATE LIMITED&#13;&#10;&#13;&#10;</BASICBASEPARTYNAME>\r\n          <PARTYGSTIN>29AABCT1296R1ZJ</PARTYGSTIN>\r\n          <PLACEOFSUPPLY>Karnataka</PLACEOFSUPPLY>\r\n          <CONSIGNEEMAILINGNAME>INGRAM MICRO INDIA PRIVATE LIMITED&#13;&#10;&#13;&#10;</CONSIGNEEMAILINGNAME>\r\n          <CONSIGNEESTATENAME>Karnataka</CONSIGNEESTATENAME>\r\n          <BASICPURCHASEORDERNO>38-F7555</BASICPURCHASEORDERNO>\r\n\r\n          <ADDRESS.LIST TYPE=\"String\">\r\n            <ADDRESS>S.No.196/2, Hulahalli, Jigani Hobli, Anekal, CK Palya,</ADDRESS>\r\n            <ADDRESS>Bangalore, 560083</ADDRESS>\r\n          </ADDRESS.LIST>\r\n\r\n          <BASICBUYERADDRESS.LIST TYPE=\"String\">\r\n            <BASICBUYERADDRESS>S.No.196/2, Hulahalli, Jigani Hobli, Anekal, CK Palya,</BASICBUYERADDRESS>\r\n            <BASICBUYERADDRESS>Bangalore, 560083</BASICBUYERADDRESS>\r\n          </BASICBUYERADDRESS.LIST>\r\n\r\n          <LEDGERENTRIES.LIST>\r\n            <LEDGERNAME>INGRAM MICRO INDIA PRIVATE LIMITED&#13;&#10;&#13;&#10;</LEDGERNAME>\r\n            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>\r\n            <ISPARTYLEDGER>Yes</ISPARTYLEDGER>\r\n            <AMOUNT>-45464.82</AMOUNT>\r\n            <BILLALLOCATIONS.LIST>\r\n              <NAME>RSNT2602</NAME>\r\n              <BILLTYPE>New Ref</BILLTYPE>\r\n              <AMOUNT>-45464.82</AMOUNT>\r\n            </BILLALLOCATIONS.LIST>\r\n          </LEDGERENTRIES.LIST>\r\n\r\n          <LEDGERENTRIES.LIST>\r\n            <LEDGERNAME>CGST</LEDGERNAME>\r\n            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>\r\n            <AMOUNT>3467.66</AMOUNT>\r\n          </LEDGERENTRIES.LIST>\r\n\r\n          <LEDGERENTRIES.LIST>\r\n            <LEDGERNAME>SGST</LEDGERNAME>\r\n            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>\r\n            <AMOUNT>3467.66</AMOUNT>\r\n          </LEDGERENTRIES.LIST>\r\n\r\n          <ALLINVENTORYENTRIES.LIST>\r\n            <STOCKITEMNAME>RSNT-RUPS-CRU12V2AU</STOCKITEMNAME>\r\n            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>\r\n            <RATE>770.59/Nos</RATE>\r\n            <AMOUNT>38529.50</AMOUNT>    \r\n            <ACTUALQTY> 50.00 Nos</ACTUALQTY>\r\n            <BILLEDQTY> 50.00 Nos</BILLEDQTY>\r\n            <BATCHALLOCATIONS.LIST>\r\n              <GODOWNNAME>Main Location</GODOWNNAME>\r\n              <BATCHNAME>Primary Batch</BATCHNAME>\r\n              <TRACKINGNUMBER>RSNT26D02</TRACKINGNUMBER>\r\n              <AMOUNT>38529.50</AMOUNT>\r\n            </BATCHALLOCATIONS.LIST>\r\n            <ACCOUNTINGALLOCATIONS.LIST>\r\n              <LEDGERNAME>B2B Sales</LEDGERNAME>\r\n              <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>\r\n              <AMOUNT>38529.50</AMOUNT>\r\n            </ACCOUNTINGALLOCATIONS.LIST>\r\n          </ALLINVENTORYENTRIES.LIST>\r\n        </VOUCHER>\r\n      </TALLYMESSAGE>\r\n    </DATA>\r\n  </BODY>\r\n</ENVELOPE>\r\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Delete Sales Voucher", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n  <HEADER>\r\n    <VERSION>1</VERSION>\r\n    <TALLYREQUEST>Import</TALLYREQUEST>\r\n    <TYPE>Data</TYPE>\r\n    <ID>Vouchers</ID>\r\n  </HEADER>\r\n  <BODY>\r\n    <DESC/>\r\n    <DATA>\r\n      <TALLYMESSAGE xmlns:UDF=\"TallyUDF\">\r\n        <VOUCHER DATE=\"01-Apr-2025\" \r\n                 TAGNAME=\"Voucher Number\"\r\n                 TAGVALUE=\"Gau01\" \r\n                 VCHTYPE=\"B2B Sales\" \r\n                 ACTION=\"Delete\"/>\r\n      </TALLYMESSAGE>\r\n    </DATA>\r\n  </BODY>\r\n</ENVELOPE>\r\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Create Delivery Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n  <HEADER>\r\n    <VERSION>1</VERSION>\r\n    <TALLYREQUEST>Import</TALLYREQUEST>\r\n    <TYPE>Data</TYPE>\r\n    <ID>Vouchers</ID>\r\n  </HEADER>\r\n  <BODY>\r\n    <DESC/>\r\n    <DATA>\r\n      <TALLYMESSAGE xmlns:UDF=\"TallyUDF\">\r\n        <VOUCHER DATE=\"01-Apr-2025\" \r\n                 TAGNAME=\"Voucher Number\"\r\n                 TAGVALUE=\"Gau01\" \r\n                 VCHTYPE=\"B2B Sales\" \r\n                 ACTION=\"Delete\"/>\r\n      </TALLYMESSAGE>\r\n    </DATA>\r\n  </BODY>\r\n</ENVELOPE>\r\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}, {"name": "Delete Delivery Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<ENVELOPE>\r\n  <HEADER>\r\n    <VERSION>1</VERSION>\r\n    <TALLYREQUEST>Import</TALLYREQUEST>\r\n    <TYPE>Data</TYPE>\r\n    <ID>Vouchers</ID>\r\n  </HEADER>\r\n  <BODY>\r\n    <DESC/>\r\n    <DATA>\r\n      <TALLYMESSAGE xmlns:UDF=\"TallyUDF\">\r\n        <VOUCHER DATE=\"25-Jul-2025\" \r\n                 TAGNAME=\"Voucher Number\"\r\n                 TAGVALUE=\"RSNT2602\" \r\n                 VCHTYPE=\"Sales\" \r\n                 ACTION=\"Delete\"/>\r\n      </TALLYMESSAGE>\r\n    </DATA>\r\n  </BODY>\r\n</ENVELOPE>\r\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:9000", "host": ["localhost"], "port": "9000"}}, "response": []}]}