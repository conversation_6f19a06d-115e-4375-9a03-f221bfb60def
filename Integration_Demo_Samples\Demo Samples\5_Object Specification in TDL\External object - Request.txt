
<ENVELOPE>
 	<HEADER>
    		<VERSION>1</VERSION>
    		<TALLYREQUEST>Export</TALLYREQUEST>
    		<TYPE>Data</TYPE>
    		<ID>Stud List</ID>
 	</HEADER>
<BODY>
	<DESC>
	<STATICVARIABLES>
     	 <EXPLODEFLAG>Yes</EXPLODEFLAG>
       	 			<SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
   		  	</STATICVARIABLES>
 <TDL>
<TDLMESSAGE>
<REPORT NAME="Stud List" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <FORMS>Stud Form</FORMS> 
  <TITLE>"List of Students"</TITLE> 
  </REPORT>
<FORM NAME="Stud Form" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <TOPPARTS>Stud Part</TOPPARTS> 
  </FORM>
 <PART NAME="Stud Part" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <TOPLINES>Stud Titles, Stud Line</TOPLINES> 
  <REPEAT>Stud Line : Student Masters</REPEAT> 
  <SCROLLED>Vertical</SCROLLED> 
  <COMMONBORDERS>Yes</COMMONBORDERS> 
  </PART>
 <LINE NAME="Stud Titles" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <USE>Stud Line</USE> 
  <LOCAL>Field: Default : Type : String</LOCAL> 
  <LOCAL>Field: Default : Align : Centre</LOCAL> 
  <LOCAL>Field: Default : Style : Normal Bold</LOCAL> 
  <LOCAL>Field: Stud Name : Set as : "Student Name"</LOCAL> 
  <LOCAL>Field: Stud JoinDate : Set as : "Joining Date"</LOCAL> 
  <LOCAL>Field: Stud FeePaid : Set as : "Fees Paid"</LOCAL> 
  <BORDER>Thin Top Bottom</BORDER> 
  </LINE>
 <LINE NAME="Stud Line" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <LEFTFIELDS>Stud JoinDate, Stud Name</LEFTFIELDS> 
  <RIGHTFIELDS>Stud FeePaid</RIGHTFIELDS> 
  </LINE>
 <FIELD NAME="Stud JoinDate" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <USE>Uni Date Field</USE> 
  <SET>$$Date:$JoiningDate</SET> 
  <WIDTH>10</WIDTH> 
  <BORDER>Thin Right</BORDER> 
  </FIELD>
 <FIELD NAME="Stud Name" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <USE>Name Field</USE> 
  <SET>$Name</SET> 
  <FULLWIDTH>Yes</FULLWIDTH> 
  </FIELD>
 <FIELD NAME="Stud FeePaid" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <USE>Amount Field</USE> 
  <BORDER>Thin Left Right</BORDER> 
  <SET>$$AsAmount:$FeePaid</SET> 
  </FIELD>
 <OBJECT NAME="Student1" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <LOCALFORMULA>Name : "Hari"</LOCALFORMULA> 
  <LOCALFORMULA>Fee Paid : 500</LOCALFORMULA> 
  <LOCALFORMULA>Joining Date : "10/01/2007"</LOCALFORMULA> 
  </OBJECT>
 <OBJECT NAME="Student2" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <LOCALFORMULA>Name : "Keshav"</LOCALFORMULA> 
  <LOCALFORMULA>Fee Paid : 150</LOCALFORMULA> 
  <LOCALFORMULA>Joining Date : "12/02/2007"</LOCALFORMULA> 
  </OBJECT>
 <OBJECT NAME="Student3" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <LOCALFORMULA>Name : "Madhav"</LOCALFORMULA> 
  <LOCALFORMULA>Fee Paid : 150</LOCALFORMULA> 
  <LOCALFORMULA>Joining Date : "10/01/2007"</LOCALFORMULA> 
  </OBJECT>
 <OBJECT NAME="Student4" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <LOCALFORMULA>Name : "Krishna"</LOCALFORMULA> 
  <LOCALFORMULA>Fee Paid : 150</LOCALFORMULA> 
  <LOCALFORMULA>Joining Date : "12/03/2007"</LOCALFORMULA> 
  </OBJECT>
 <OBJECT NAME="Student5" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <LOCALFORMULA>Name : "Gopal"</LOCALFORMULA> 
  <LOCALFORMULA>Fee Paid : 150</LOCALFORMULA> 
  <LOCALFORMULA>Joining Date : "12/02/2007"</LOCALFORMULA> 
  </OBJECT>
 <COLLECTION NAME="Student Masters" ISMODIFY="No" ISFIXED="No" ISINITIALIZE="No" ISOPTION="No" ISINTERNAL="No">
  <OBJECTS>Student1, Student2, Student3, Student4, Student5</OBJECTS> 
  </COLLECTION>
  </TDLMESSAGE>
  </TDL>
  </DESC>
 </BODY>
</ENVELOPE>