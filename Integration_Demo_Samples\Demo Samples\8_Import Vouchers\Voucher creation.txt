<ENVELOPE>
	<HEADER>
		<VERSION>1</VERSION>
		<TALLYREQUEST>Import</TALLYREQUEST>
		<TYPE>Data</TYPE>
		<ID>Vouchers</ID>
	</HEADER>
	<BODY>
		<DESC>
			<STATICVARIABLES>
  				 <IMPORTDUPS>@@DUPCOMBINE</IMPORTDUPS>
 		     	</STATICVARIABLES>
		</DESC>
		<DATA>
			<TALLYMESSAGE>
				<VOUCHER>
					<DATE>********</DATE>
					<NARRATION>Ch. No. Tested</NARRATION>
					<VOUCHERTYPENAME>Payment</VOUCHERTYPENAME>
					<VOUCHERNUMBER>1</VOUCHERNUMBER>
					<ALLLEDGERENTRIES.LIST>
						<LEDGERNAME>Conveyance</LEDGERNAME>
						<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
						<AMOUNT>12000.00</AMOUNT>
					</ALLLEDGERENTRIES.LIST>
					<ALLLEDGERENTRIES.LIST>
						<LEDGERNAME>Bank of India</LEDGERNAME>
						<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
						<AMOUNT>-12000.00</AMOUNT>
					</ALLLEDGERENTRIES.LIST>
				</VOUCHER>
				<VOUCHER>
					<DATE>********</DATE>
					<NARRATION>Ch. No. : Tested</NARRATION>
					<VOUCHERTYPENAME>Payment</VOUCHERTYPENAME>
					<VOUCHERNUMBER>2</VOUCHERNUMBER>
					<ALLLEDGERENTRIES.LIST>
						<LEDGERNAME>Conveyance</LEDGERNAME>
						<ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
						<AMOUNT>-5000.00</AMOUNT>
					</ALLLEDGERENTRIES.LIST>
					<ALLLEDGERENTRIES.LIST>
						<LEDGERNAME>Bank of India</LEDGERNAME>
						<ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
						<AMOUNT>5000.00</AMOUNT>
					</ALLLEDGERENTRIES.LIST>
				</VOUCHER>
			</TALLYMESSAGE>
		</DATA>
	</BODY>
</ENVELOPE>