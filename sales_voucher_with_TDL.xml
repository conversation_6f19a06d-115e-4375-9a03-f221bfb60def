<ENVELOPE>
  <HEADER>
    <VERSION>1</VERSION>
    <TALLYREQUEST>EXPORT</TALLYREQUEST>
    <TYPE>DATA</TYPE>

    <!--Report Name from the Data needs to extracted. This is custom created through TDL. -->
    <ID>TC_SALES</ID>
  </HEADER>
  <BODY>
    <DESC>
      <STATICVARIABLES>
        <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>

        <!--Company from the Data needs to extracted-->
        <SVCURRENTCOMPANY>Resonate Systems Private Limited</SVCURRENTCOMPANY>

        <!--Date Range from the Data needs to extracted-->
        <SVFROMDATE>20250724</SVFROMDATE>
        <SVTODATE>20250725</SVTODATE>

      </STATICVARIABLES>

      <!--TDL Report Hierarchy (Report -> Form -> Part -> Line -> Fields)-->
      <TDL>
        <TDLMESSAGE>

          <!-- Report with Name TC_SALES -->
          <REPORT NAME="TC_SALES">
            <FORM>TC_SALES</FORM>
          </REPORT>

          <!-- Form with Name TC_SALES -->
          <FORM NAME="TC_SALES">
            <PART>TC_SALES</PART>
            <XMLTAG>VOUCHER.LIST</XMLTAG>
          </FORM>

          <!-- Voucher Part -->
          <PART NAME="TC_SALES">
            <LINE>TC_SALES</LINE>
            <REPEAT>TC_SALES:TC_VOUCHERCOLLECTION</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Lines of PART TC_SALES -->
          <LINE NAME="TC_SALES">
            <FIELDS>
              TC_VOUCHERTYPE, TC_VOUCHERNUMBER, TC_DATE, TC_BASICSHIPDOCUMENTNO, TC_BASICDUEDATEOFPYMT, TC_REFERENCE, 
              TC_BASICSHIPPEDBY, TC_CONSIGNEEMAILINGNAME, TC_CONSIGNEESTATENAME, TC_PARTYLEDGERNAME, TC_BASICBUYERNAME, TC_BASICBASEPARTYNAME,
              TC_PARTYGSTIN, TC_PLACEOFSUPPLY, 
              TC_BASICFINALDESTINATION, TC_CONSIGNEECOUNTRYNAME, TC_BASICSHIPPEDBY, TC_STATENAME
            </FIELDS>
            <EXPLODE>TC_VOUCHERLEDGER:Yes</EXPLODE>
            <EXPLODE>TC_VOUCHERINVENTORY:Yes</EXPLODE>
            <EXPLODE>TC_INVOICEORDER:Yes</EXPLODE>
            <EXPLODE>TC_INVOICEDELNOTES:Yes</EXPLODE>
            <EXPLODE>TC_ADDRESSES:Yes</EXPLODE>
            <EXPLODE>TC_BASICBUYERADDRESS:Yes</EXPLODE>
            <XMLTAG>VOUCHER</XMLTAG>
          </LINE>

          <!-- Fields belong TC_SALES -->

          <!-- 1. Voucher Type-->
          <FIELD NAME="TC_VOUCHERTYPE"><SET>$VoucherTypeName</SET><XMLTAG>VOUCHERTYPENAME</XMLTAG></FIELD>

          <!-- 2. Invoice Number -->
          <FIELD NAME="TC_VOUCHERNUMBER"><SET>$VoucherNumber</SET><XMLTAG>VOUCHERNUMBER</XMLTAG></FIELD>

          <!--3. Dated -->
          <FIELD NAME="TC_DATE"><SET>$Date</SET><XMLTAG>DATE</XMLTAG></FIELD>

          <!--13 Dispatch Document No -->
          <FIELD NAME="TC_BASICSHIPDOCUMENTNO"><SET>$BasicShipDocumentNo</SET><XMLTAG>BASICSHIPDOCUMENTNO</XMLTAG></FIELD>

          <!--5. Mode/Terms of Payment -->
          <FIELD NAME="TC_BASICDUEDATEOFPYMT"><SET>$BasicDueDateOfPymt</SET><XMLTAG>BASICDUEDATEOFPYMT</XMLTAG></FIELD>

          <!--6. Reference -->
          <FIELD NAME="TC_REFERENCE"><SET>$Reference</SET><XMLTAG>REFERENCE</XMLTAG></FIELD>

          <!--8. Consignee -->
          <FIELD NAME="TC_CONSIGNEEMAILINGNAME"><SET>$ConsigneeMailingName</SET><XMLTAG>CONSIGNEEMAILINGNAME</XMLTAG></FIELD>

          <!--10. COnsignee State -->
          <FIELD NAME="TC_CONSIGNEESTATENAME"><SET>$ConsigneeStateName</SET><XMLTAG>CONSIGNEESTATENAME</XMLTAG></FIELD>

          <!--15. Dispatched Through -->
          <FIELD NAME="TC_BASICSHIPPEDBY"><SET>$BasicShippedBy</SET><XMLTAG>BASICSHIPPEDBY</XMLTAG></FIELD>

          <FIELD NAME="TC_PARTYLEDGERNAME"><SET>$PartyLedgerName</SET><XMLTAG>PARTYLEDGERNAME</XMLTAG></FIELD>

          <!--17. Buyer Name -->
          <FIELD NAME="TC_BASICBUYERNAME"><SET>$BasicBuyerName</SET><XMLTAG>BASICBUYERNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICBASEPARTYNAME"><SET>$BasicBasePartyName</SET><XMLTAG>BASICBASEPARTYNAME</XMLTAG></FIELD>

          <!--19. GSTIN of Supplier -->
          <FIELD NAME="TC_PARTYGSTIN"><SET>$PartyGSTIN</SET><XMLTAG>PARTYGSTIN</XMLTAG></FIELD>

          
          <FIELD NAME="TC_PLACEOFSUPPLY"><SET>$PlaceOfSupply</SET><XMLTAG>PLACEOFSUPPLY</XMLTAG></FIELD>
          

          <!--16. Destination -->
          <FIELD NAME="TC_BASICFINALDESTINATION"><SET>$BasicFinalDestination</SET><XMLTAG>BASICFINALDESTINATION</XMLTAG></FIELD>
        

          <FIELD NAME="TC_CONSIGNEECOUNTRYNAME"><SET>$ConsigneeCountryName</SET><XMLTAG>CONSIGNEECOUNTRYNAME</XMLTAG></FIELD>

          <!--20. State -->
          <FIELD NAME="TC_STATENAME"><SET>$StateName</SET><XMLTAG>STATENAME</XMLTAG></FIELD>

          <!-- Ledger Entries Part -->
          <PART NAME="TC_VOUCHERLEDGER">
            <LINE>TC_VOUCHERLEDGER</LINE>
            <REPEAT>TC_VOUCHERLEDGER:ALLLEDGERENTRIES</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <LINE NAME="TC_VOUCHERLEDGER">
            <FIELDS>TC_LEDGERNAME, TC_AMOUNT</FIELDS>
            <EXPLODE>TC_RATEOFINVOICETAX:Yes</EXPLODE>
            <XMLTAG>ALLLEDGERENTRIES.LIST</XMLTAG>
          </LINE>

          <!-- Ledger Fields -->

          <!-- 26, 29 Ledger Name -->
          <FIELD NAME="TC_LEDGERNAME"><SET>$LedgerName</SET><XMLTAG>LEDGERNAME</XMLTAG></FIELD>
          
          <!-- 28, 30, 33 This field contains the Amount against the Ledger -->
          <FIELD NAME="TC_AMOUNT"><SET>$Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>

          <!-- 27, 30. Rate Of Invoice Tax -->
          <PART NAME="TC_RATEOFINVOICETAX">
              <LINE>TC_RATEOFINVOICETAX</LINE>
              <REPEAT>TC_RATEOFINVOICETAX:RATEOFINVOICETAX</REPEAT>
              <SCROLLED>Vertical</SCROLLED>
              <XMLTAG>RATEOFINVOICETAX.LIST</XMLTAG>
          </PART>

          <LINE NAME="TC_RATEOFINVOICETAX">
              <FIELDS>TC_RATEOFINVOICETAX</FIELDS>
          </LINE>

          <!-- 27, 30. Rate Of Invoice Tax Field -->
          <FIELD NAME="TC_RATEOFINVOICETAX">
              <SET>$RateOfInvoiceTax</SET>
              <XMLTAG>RATEOFINVOICETAX</XMLTAG>
          </FIELD>

          <!-- Inventory Entries Part -->
          <PART NAME="TC_VOUCHERINVENTORY">
            <LINE>TC_VOUCHERINVENTORY</LINE>
            <REPEAT>TC_VOUCHERINVENTORY:ALLINVENTORYENTRIES</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <LINE NAME="TC_VOUCHERINVENTORY">
            <FIELDS>
              TC_STOCKITEMNAME, TC_ISDEEMEDPOSITIVE, TC_RATE, TC_INVENTORY_AMOUNT,
              TC_ACTUALQTY, TC_BILLEDQTY, TC_GODOWNNAME, TC_BATCHNAME,
              TC_TRACKINGNUMBER, TC_BATCH_AMOUNT, TC_SALES_LEDGERNAME,
              TC_SALES_ISDEEMEDPOSITIVE, TC_SALES_AMOUNT, TC_GST_HSNNAME
            </FIELDS>
            <XMLTAG>ALLINVENTORYENTRIES.LIST</XMLTAG>
          </LINE>

          <!-- Inventory Fields -->

          <!-- 21. Description of Goods -->
          <FIELD NAME="TC_STOCKITEMNAME"><SET>$StockItemName</SET><XMLTAG>STOCKITEMNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_ISDEEMEDPOSITIVE"><SET>$IsDeemedPositive</SET><XMLTAG>ISDEEMEDPOSITIVE</XMLTAG></FIELD>

          <!-- 24. Rate -->
          <FIELD NAME="TC_RATE"><SET>$Rate</SET><XMLTAG>RATE</XMLTAG></FIELD>
          <FIELD NAME="TC_INVENTORY_AMOUNT"><SET>$Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>

          <FIELD NAME="TC_ACTUALQTY"><SET>$ActualQty</SET><XMLTAG>ACTUALQTY</XMLTAG></FIELD>

          <!-- 23. Quantity -->
          <FIELD NAME="TC_BILLEDQTY"><SET>$BilledQty</SET><XMLTAG>BILLEDQTY</XMLTAG></FIELD>
          <FIELD NAME="TC_GODOWNNAME"><SET>$BatchAllocations.GodownName</SET><XMLTAG>GODOWNNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_BATCHNAME"><SET>$BatchAllocations.BatchName</SET><XMLTAG>BATCHNAME</XMLTAG></FIELD>

          <!--4. DELIVERY NOTE NO -->
          <FIELD NAME="TC_TRACKINGNUMBER"><SET>$BatchAllocations.TrackingNumber</SET><XMLTAG>TRACKINGNUMBER</XMLTAG></FIELD>

          <!-- 25. Batch Amount -->
          <FIELD NAME="TC_BATCH_AMOUNT"><SET>$BatchAllocations.Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>
          <FIELD NAME="TC_SALES_LEDGERNAME"><SET>$AccountingAllocations.LedgerName</SET><XMLTAG>LEDGERNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_SALES_ISDEEMEDPOSITIVE"><SET>$AccountingAllocations.IsDeemedPositive</SET><XMLTAG>ISDEEMEDPOSITIVE</XMLTAG></FIELD>
          <FIELD NAME="TC_SALES_AMOUNT"><SET>$AccountingAllocations.Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>

          <!-- 22. HSN Code -->
          <FIELD NAME="TC_GST_HSNNAME"><SET>$GSTHSNName</SET><XMLTAG>GSTHSNNAME</XMLTAG></FIELD>

          <!-- Invoice Order Part -->
          <PART NAME="TC_INVOICEORDER">
            <LINE>TC_INVOICEORDER</LINE>
            <REPEAT>TC_INVOICEORDER:INVOICEORDERLIST</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <LINE NAME="TC_INVOICEORDER">
            <FIELDS>TC_BASICORDERDATE, TC_BASICPURCHASEORDERNO</FIELDS>
            <XMLTAG>INVOICEORDERLIST.LIST</XMLTAG>
          </LINE>

          <!--12. Buyers Date  -->
          <FIELD NAME="TC_BASICORDERDATE"><SET>$BasicOrderDate</SET><XMLTAG>ORDERDATE</XMLTAG></FIELD>

          <!--11. Buyers Order No -->
          <FIELD NAME="TC_BASICPURCHASEORDERNO"><SET>$BasicPurchaseOrderNo</SET><XMLTAG>PURCHASEORDERNO</XMLTAG></FIELD>  

          <!-- Invoice Delivery Notes Part -->
          <PART NAME="TC_INVOICEDELNOTES">
            <LINE>TC_INVOICEDELNOTES</LINE>
            <REPEAT>TC_INVOICEDELNOTES:INVOICEDELNOTES</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
            <XMLTAG>INVOICEDELNOTES.LIST</XMLTAG>
          </PART>

          <LINE NAME="TC_INVOICEDELNOTES">
            <FIELDS>TC_BASICSHIPPINGDATE, TC_BASICSHIPDELIVERYNOTE</FIELDS>
          </LINE>

          <!-- 4. Delivery Date -->
          <FIELD NAME="TC_BASICSHIPPINGDATE"><SET>$BasicShippingDate</SET><XMLTAG>BASICSHIPPINGDATE</XMLTAG></FIELD>

          <!-- 14. Delivery Note No -->
          <FIELD NAME="TC_BASICSHIPDELIVERYNOTE"><SET>$BasicShipDeliveryNote</SET><XMLTAG>BASICSHIPDELIVERYNOTE</XMLTAG></FIELD>

          <!-- 9. Addresses Part -->
          <PART NAME="TC_ADDRESSES">
            <LINE>TC_ADDRESS_LINE</LINE>
            <REPEAT>TC_ADDRESS_LINE:ADDRESS</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
            <XMLTAG>ADDRESS.LIST</XMLTAG>
          </PART>

          <LINE NAME="TC_ADDRESS_LINE">
            <FIELDS>TC_ADDRESS</FIELDS>
          </LINE>

          <!-- Address Fields -->
          <FIELD NAME="TC_ADDRESS"><SET>$Address</SET><XMLTAG>ADDRESS</XMLTAG></FIELD>

          <!--18 Buyer Address Part -->
          <PART NAME="TC_BASICBUYERADDRESS">
            <LINE>TC_BASICBUYERADDRESS_LINE</LINE>
            <REPEAT>TC_BASICBUYERADDRESS_LINE:BASICBUYERADDRESS</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
            <XMLTAG>BASICBUYERADDRESS.LIST</XMLTAG>
          </PART>

          <LINE NAME="TC_BASICBUYERADDRESS_LINE">
            <FIELDS>TC_BASICBUYERADDRESS</FIELDS>
          </LINE>

          <!-- 18. Buyer Address Fields -->
          <FIELD NAME="TC_BASICBUYERADDRESS"><SET>$BasicBuyerAddress</SET><XMLTAG>BASICBUYERADDRESS</XMLTAG></FIELD>

          <!-- Collection -->
          <COLLECTION NAME="TC_VOUCHERCOLLECTION">
            <TYPE>Vouchers</TYPE>
            <FILTERS>IsSalesVoucher</FILTERS>

            <!-- Ordered according to Fields -->
            <NATIVEMETHOD>VOUCHERTYPENAME</NATIVEMETHOD>
            <NATIVEMETHOD>VOUCHERNUMBER</NATIVEMETHOD>
            <NATIVEMETHOD>DATE</NATIVEMETHOD>
            <NATIVEMETHOD>BASICSHIPDOCUMENTNO</NATIVEMETHOD>
            <NATIVEMETHOD>BASICDUEDATEOFPYMT</NATIVEMETHOD>
            <NATIVEMETHOD>REFERENCE</NATIVEMETHOD>
            <NATIVEMETHOD>BASICSHIPPEDBY</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEEMAILINGNAME</NATIVEMETHOD>
            <NATIVEMETHOD>PARTYLEDGERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICBUYERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICBASEPARTYNAME</NATIVEMETHOD>
            <NATIVEMETHOD>PARTYGSTIN</NATIVEMETHOD>
            <NATIVEMETHOD>PLACEOFSUPPLY</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEESTATENAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICFINALDESTINATION</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEECOUNTRYNAME</NATIVEMETHOD>
            <NATIVEMETHOD>STATENAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLLEDGERENTRIES.LEDGERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLLEDGERENTRIES.AMOUNT</NATIVEMETHOD>
            <NATIVEMETHOD>RATEOFINVOICETAX</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.STOCKITEMNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ISDEEMEDPOSITIVE</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.RATE</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.AMOUNT</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACTUALQTY</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BILLEDQTY</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.GODOWNNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.BATCHNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.TRACKINGNUMBER</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.AMOUNT</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACCOUNTINGALLOCATIONS.LEDGERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACCOUNTINGALLOCATIONS.ISDEEMEDPOSITIVE</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACCOUNTINGALLOCATIONS.AMOUNT</NATIVEMETHOD>
            <NATIVEMETHOD>INVOICEORDERLIST.BASICORDERDATE</NATIVEMETHOD>
            <NATIVEMETHOD>INVOICEORDERLIST.BASICPURCHASEORDERNO</NATIVEMETHOD>
            <NATIVEMETHOD>INVOICEDELNOTES.BASICSHIPPINGDATE</NATIVEMETHOD>
            <NATIVEMETHOD>INVOICEDELNOTES.BASICSHIPDELIVERYNOTE</NATIVEMETHOD>
            <NATIVEMETHOD>ADDRESS</NATIVEMETHOD>
            <NATIVEMETHOD>BASICBUYERADDRESS</NATIVEMETHOD>
          </COLLECTION>

        </TDLMESSAGE>
      </TDL>
    </DESC>
  </BODY>
</ENVELOPE>
