<ENVELOPE>
  <HEADER>
    <VERSION>1</VERSION>
    <TALLYREQUEST>EXPORT</TALLYREQUEST>
    <TYPE>DATA</TYPE>
    <ID>TC_DELIVERYNOTE</ID>
  </HEADER>
  <BODY>
    <DESC>
      <STATICVARIABLES>
        <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
      </STATICVARIABLES>
      <TDL>
        <TDLMESSAGE>
        
          <!-- Report -->
          <REPORT NAME="TC_DELIVERYNOTE">
            <FORM>TC_DELIVERYNOTE</FORM>
          </REPORT>

          <!-- Form -->
          <FORM NAME="TC_DELIVERYNOTE">
            <PART>TC_DELIVERYNOTE</PART>
            <XMLTAG>VOUCHER.LIST</XMLTAG>
          </FORM>

          <!-- Voucher Part -->
          <PART NAME="TC_DELIVERYNOTE">
            <LINE>TC_DELIVERYNOTE</LINE>
            <REPEAT>TC_DELIVERYNOTE:TC_DELIVERYCOLL</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Ledger Entries -->
          <PART NAME="TC_LEDGER">
            <LINE>TC_LEDGER</LINE>
            <REPEAT>TC_LEDGER:LEDGERENTRIES</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Inventory Entries -->
          <PART NAME="TC_INVENTORY">
            <LINE>TC_INVENTORY</LINE>
            <REPEAT>TC_INVENTORY:ALLINVENTORYENTRIES</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <PART NAME="TC_ACCOUNTINGALLOCATIONS">
            <LINE>TC_ACCOUNTINGALLOCATIONS</LINE>
            <REPEAT>TC_ACCOUNTINGALLOCATIONS:ACCOUNTINGALLOCATIONS</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Order Part -->
          <PART NAME="TC_ORDER">
            <LINE>TC_ORDER</LINE>
            <REPEAT>TC_ORDER:INVOICEORDERLIST</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Address Part -->
          <PART NAME="TC_ADDRESS">
            <LINE>TC_ADDRESS</LINE>
            <REPEAT>TC_ADDRESS:ADDRESS</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Line Definitions -->
          <LINE NAME="TC_DELIVERYNOTE">
            <FIELDS>
              D_DATE, D_VOUCHERTYPENAME, D_VOUCHERNUMBER,
              D_PARTYLEDGERNAME, D_BASICBUYERNAME, D_BASICBASEPARTYNAME,
              D_PARTYGSTIN, D_PLACEOFSUPPLY, D_CONSIGNEEMAILINGNAME, D_CONSIGNEESTATENAME,
              D_BASICSHIPDOCUMENTNO, D_BASICFINALDESTINATION,
              D_BASICDUEDATEOFPYMT, D_CONSIGNEECOUNTRYNAME,
              D_REFERENCE, D_BASICSHIPPEDBY, D_REFERENCEDATE
            </FIELDS>
            <EXPLODE>TC_LEDGER:Yes</EXPLODE>
            <EXPLODE>TC_INVENTORY:Yes</EXPLODE>
            <EXPLODE>TC_ORDER:Yes</EXPLODE>
            <EXPLODE>TC_ADDRESS:Yes</EXPLODE>
            <XMLTAG>VOUCHER</XMLTAG>
          </LINE>

          <!-- Ledger Line -->
          <LINE NAME="TC_LEDGER">
            <FIELDS>D_LEDGERNAME, D_ISDEEMEDPOSITIVE, D_ISPARTYLEDGER, D_LEDGERAMOUNT</FIELDS>
            <XMLTAG>LEDGERENTRIES.LIST</XMLTAG>
          </LINE>

          <!-- Inventory Line -->
          <LINE NAME="TC_INVENTORY">
            <FIELDS>
              D_STOCKITEMNAME, D_INV_ISDEEMEDPOSITIVE, D_RATE, D_INV_AMOUNT,
              D_ACTUALQTY, D_BILLEDQTY,
              D_GODOWNNAME, D_BATCHNAME, D_TRACKINGNUMBER,
              D_BATCH_AMOUNT, D_BATCH_ACTUALQTY, D_BATCH_BILLEDQTY
            </FIELDS>
            <EXPLODE>TC_ACCOUNTINGALLOCATIONS:Yes</EXPLODE>
            <XMLTAG>ALLINVENTORYENTRIES.LIST</XMLTAG>
          </LINE>

          <LINE NAME="TC_ACCOUNTINGALLOCATIONS">
            <FIELDS>D_ACC_LEDGERNAME, D_ACC_ISDEEMEDPOSITIVE, D_ACC_AMOUNT</FIELDS>
            <XMLTAG>ACCOUNTINGALLOCATIONS.LIST</XMLTAG>
          </LINE>

          <!-- Order Line -->
          <LINE NAME="TC_ORDER">
            <FIELDS>D_BASICORDERDATE, D_BASICPURCHASEORDERNO</FIELDS>
            <XMLTAG>INVOICEORDERLIST.LIST</XMLTAG>
          </LINE>
          <!-- Address Line -->
          <LINE NAME="TC_ADDRESS">    
            <FIELDS>D_ADDRESS</FIELDS>
            <XMLTAG>ADDRESS.LIST</XMLTAG>
          </LINE>

          <!-- Fields -->
          <FIELD NAME="D_DATE"><SET>$Date</SET><XMLTAG>DATE</XMLTAG></FIELD>
          <FIELD NAME="D_VOUCHERTYPENAME"><SET>$VoucherTypeName</SET><XMLTAG>VOUCHERTYPENAME</XMLTAG></FIELD>
          <FIELD NAME="D_VOUCHERNUMBER"><SET>$VoucherNumber</SET><XMLTAG>VOUCHERNUMBER</XMLTAG></FIELD>
          <FIELD NAME="D_PARTYLEDGERNAME"><SET>$PartyLedgerName</SET><XMLTAG>PARTYLEDGERNAME</XMLTAG></FIELD>
          <FIELD NAME="D_BASICBUYERNAME"><SET>$BasicBuyerName</SET><XMLTAG>BASICBUYERNAME</XMLTAG></FIELD>
          <FIELD NAME="D_BASICBASEPARTYNAME"><SET>$BasicBasePartyName</SET><XMLTAG>BASICBASEPARTYNAME</XMLTAG></FIELD>
          <FIELD NAME="D_PARTYGSTIN"><SET>$PartyGSTIN</SET><XMLTAG>PARTYGSTIN</XMLTAG></FIELD>
          <FIELD NAME="D_PLACEOFSUPPLY"><SET>$PlaceOfSupply</SET><XMLTAG>PLACEOFSUPPLY</XMLTAG></FIELD>
          <FIELD NAME="D_CONSIGNEEMAILINGNAME"><SET>$ConsigneeMailingName</SET><XMLTAG>CONSIGNEEMAILINGNAME</XMLTAG></FIELD>
          <FIELD NAME="D_CONSIGNEESTATENAME"><SET>$ConsigneeStateName</SET><XMLTAG>CONSIGNEESTATENAME</XMLTAG></FIELD>
          <FIELD NAME="D_BASICSHIPDOCUMENTNO"><SET>$BasicShipDocumentNo</SET><XMLTAG>BASICSHIPDOCUMENTNO</XMLTAG></FIELD>
          <FIELD NAME="D_BASICFINALDESTINATION"><SET>$BasicFinalDestination</SET><XMLTAG>BASICFINALDESTINATION</XMLTAG></FIELD>
          <FIELD NAME="D_BASICDUEDATEOFPYMT"><SET>$BasicDueDateOfPymt</SET><XMLTAG>BASICDUEDATEOFPYMT</XMLTAG></FIELD>
          <FIELD NAME="D_CONSIGNEECOUNTRYNAME"><SET>$ConsigneeCountryName</SET><XMLTAG>CONSIGNEECOUNTRYNAME</XMLTAG></FIELD>
          <FIELD NAME="D_REFERENCE"><SET>$Reference</SET><XMLTAG>REFERENCE</XMLTAG></FIELD>
          <FIELD NAME="D_BASICSHIPPEDBY"><SET>$BasicShippedBy</SET><XMLTAG>BASICSHIPPEDBY</XMLTAG></FIELD>
          <FIELD NAME="D_REFERENCEDATE"><SET>$ReferenceDate</SET><XMLTAG>REFERENCEDATE</XMLTAG></FIELD>

          <!-- Ledger Fields -->
          <FIELD NAME="D_LEDGERNAME"><SET>$LedgerName</SET><XMLTAG>LEDGERNAME</XMLTAG></FIELD>
          <FIELD NAME="D_ISDEEMEDPOSITIVE"><SET>$IsDeemedPositive</SET><XMLTAG>ISDEEMEDPOSITIVE</XMLTAG></FIELD>
          <FIELD NAME="D_ISPARTYLEDGER"><SET>$IsPartyLedger</SET><XMLTAG>ISPARTYLEDGER</XMLTAG></FIELD>
          <FIELD NAME="D_LEDGERAMOUNT"><SET>$Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>

          <!-- Inventory Fields -->
          <FIELD NAME="D_STOCKITEMNAME"><SET>$StockItemName</SET><XMLTAG>STOCKITEMNAME</XMLTAG></FIELD>
          <FIELD NAME="D_INV_ISDEEMEDPOSITIVE"><SET>$IsDeemedPositive</SET><XMLTAG>ISDEEMEDPOSITIVE</XMLTAG></FIELD>
          <FIELD NAME="D_RATE"><SET>$Rate</SET><XMLTAG>RATE</XMLTAG></FIELD>
          <FIELD NAME="D_INV_AMOUNT"><SET>$Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>
          <FIELD NAME="D_ACTUALQTY"><SET>$ActualQty</SET><XMLTAG>ACTUALQTY</XMLTAG></FIELD>
          <FIELD NAME="D_BILLEDQTY"><SET>$BilledQty</SET><XMLTAG>BILLEDQTY</XMLTAG></FIELD>
          
          <FIELD NAME="D_GODOWNNAME"><SET>$BatchAllocations.GodownName</SET><XMLTAG>GODOWNNAME</XMLTAG></FIELD>
          <FIELD NAME="D_BATCHNAME"><SET>$BatchAllocations.BatchName</SET><XMLTAG>BATCHNAME</XMLTAG></FIELD>
          <FIELD NAME="D_TRACKINGNUMBER"><SET>$BatchAllocations.TrackingNumber</SET><XMLTAG>TRACKINGNUMBER</XMLTAG></FIELD>
          <FIELD NAME="D_BATCH_AMOUNT"><SET>$BatchAllocations.Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>
          <FIELD NAME="D_BATCH_ACTUALQTY"><SET>$BatchAllocations.ActualQty</SET><XMLTAG>ACTUALQTY</XMLTAG></FIELD>
          <FIELD NAME="D_BATCH_BILLEDQTY"><SET>$BatchAllocations.BilledQty</SET><XMLTAG>BILLEDQTY</XMLTAG></FIELD>

          <FIELD NAME="D_ACC_LEDGERNAME"><SET>$AccountingAllocations.LedgerName</SET><XMLTAG>LEDGERNAME</XMLTAG></FIELD>
          <FIELD NAME="D_ACC_ISDEEMEDPOSITIVE"><SET>$AccountingAllocations.IsDeemedPositive</SET><XMLTAG>ISDEEMEDPOSITIVE</XMLTAG></FIELD>
          <FIELD NAME="D_ACC_AMOUNT"><SET>$AccountingAllocations.Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>

          <!-- Order Fields -->
          <FIELD NAME="D_BASICORDERDATE"><SET>$BasicOrderDate</SET><XMLTAG>BASICORDERDATE</XMLTAG></FIELD>
          <FIELD NAME="D_BASICPURCHASEORDERNO"><SET>$BasicPurchaseOrderNo</SET><XMLTAG>BASICPURCHASEORDERNO</XMLTAG></FIELD>

          <!-- Address Fields -->
          <FIELD NAME="D_ADDRESS"><SET>$Address</SET><XMLTAG>ADDRESS</XMLTAG></FIELD>

          <!-- Collection -->
          <COLLECTION NAME="TC_DELIVERYCOLL">
            <TYPE>Vouchers</TYPE>
            <FILTERS>IsDeliveryNote</FILTERS>
            <NATIVEMETHOD>DATE</NATIVEMETHOD>
            <NATIVEMETHOD>VOUCHERTYPENAME</NATIVEMETHOD>
            <NATIVEMETHOD>VOUCHERNUMBER</NATIVEMETHOD>
            <NATIVEMETHOD>PARTYLEDGERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICBUYERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICBASEPARTYNAME</NATIVEMETHOD>
            <NATIVEMETHOD>PARTYGSTIN</NATIVEMETHOD>
            <NATIVEMETHOD>PLACEOFSUPPLY</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEEMAILINGNAME</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEESTATENAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICSHIPDOCUMENTNO</NATIVEMETHOD>
            <NATIVEMETHOD>BASICFINALDESTINATION</NATIVEMETHOD>
            <NATIVEMETHOD>BASICDUEDATEOFPYMT</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEECOUNTRYNAME</NATIVEMETHOD>
            <NATIVEMETHOD>REFERENCE</NATIVEMETHOD>
            <NATIVEMETHOD>BASICSHIPPEDBY</NATIVEMETHOD>
            <NATIVEMETHOD>REFERENCEDATE</NATIVEMETHOD>
            <NATIVEMETHOD>LEDGERENTRIES.LIST</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.LIST</NATIVEMETHOD>
            <NATIVEMETHOD>INVOICEORDERLIST.LIST</NATIVEMETHOD> 
            <NATIVEMETHOD>ADDRESS</NATIVEMETHOD>
            

          </COLLECTION>

          <!-- Filter -->
          <SYSTEM TYPE="Formulae" NAME="IsDeliveryNote">$VoucherTypeName = "Delivery Note"</SYSTEM>


        </TDLMESSAGE>
      </TDL>
    </DESC>
  </BODY>
</ENVELOPE>
