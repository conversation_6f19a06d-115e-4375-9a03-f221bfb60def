<ENVELOPE>
  <HEADER>
    <VERSION>1</VERSION>
    <TALLYREQUEST>EXPORT</TALLYREQUEST>
    <TYPE>DATA</TYPE>
    <ID>TC_VOUCHER</ID>
  </HEADER>
  <BODY>
    <DESC>
      <STATICVARIABLES>
        <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
        <SVFROMDATE>20250401</SVFROMDATE>
        <SVTODATE>20250811</SVTODATE>
        <SVCURRENTCOMPANY>Resonate Systems Private Limited</SVCURRENTCOMPANY>
      </STATICVARIABLES>
      <TDL>
        <TDLMESSAGE>

          <!-- Report -->
          <REPORT NAME="TC_VOUCHER">
            <FORM>TC_VOUCHER</FORM>
          </REPORT>

          <!-- Form -->
          <FORM NAME="TC_VOUCHER">
            <PART>TC_VOUCHER</PART>
            <XMLTAG>VOUCHER.LIST</XMLTAG>
          </FORM>

          <!-- Voucher Part -->
          <PART NAME="TC_VOUCHER">
            <LINE>TC_VOUCHER</LINE>
            <REPEAT>TC_VOUCHER:TC_VOUCHERCOLLECTION</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Ledger Entries Part -->
          <PART NAME="TC_VOUCHERLEDGER">
            <LINE>TC_VOUCHERLEDGER</LINE>
            <REPEAT>TC_VOUCHERLEDGER:ALLLEDGERENTRIES</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Inventory Entries Part -->
          <PART NAME="TC_VOUCHERINVENTORY">
            <LINE>TC_VOUCHERINVENTORY</LINE>
            <REPEAT>TC_VOUCHERINVENTORY:ALLINVENTORYENTRIES</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <PART NAME="TC_INVOICEORDER">
            <LINE>TC_INVOICEORDER</LINE>
            <REPEAT>TC_INVOICEORDER:INVOICEORDERLIST</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <PART NAME="TC_ADDRESSES">
            <LINE>TC_ADDRESSES</LINE>
            <REPEAT>TC_ADDRESSES:ADDRESS</REPEAT>
            <SCROLLED>Vertical</SCROLLED>
          </PART>

          <!-- Lines -->
          <LINE NAME="TC_VOUCHER">
            <FIELDS>
              TC_GUID, TC_ALTERID, TC_MASTERID, TC_DATE, TC_VOUCHERTYPE,
              TC_VOUCHERNUMBER, TC_PARTYLEDGERNAME, TC_BASICBUYERNAME, TC_BASICBASEPARTYNAME,
              TC_PARTYGSTIN, TC_PLACEOFSUPPLY, TC_CONSIGNEEMAILINGNAME, TC_CONSIGNEESTATENAME,
              TC_BASICSHIPDOCUMENTNO, TC_BASICFINALDESTINATION, TC_BASICDUEDATEOFPYMT, TC_CONSIGNEECOUNTRYNAME, TC_REFERENCE, TC_BASICSHIPPEDBY
            </FIELDS>
            <EXPLODE>TC_VOUCHERLEDGER:Yes</EXPLODE>
            <EXPLODE>TC_VOUCHERINVENTORY:Yes</EXPLODE>
            <EXPLODE>TC_INVOICEORDER:Yes</EXPLODE>
            <EXPLODE>TC_ADDRESSES:Yes</EXPLODE>
            <XMLTAG>VOUCHER</XMLTAG>
          </LINE>

          <LINE NAME="TC_VOUCHERLEDGER">
            <FIELDS>TC_LEDGERNAME, TC_AMOUNT</FIELDS>
            <XMLTAG>ALLLEDGERENTRIES.LIST</XMLTAG>
          </LINE>

          <LINE NAME="TC_VOUCHERINVENTORY">
            <FIELDS>
              TC_STOCKITEMNAME, TC_ISDEEMEDPOSITIVE, TC_RATE, TC_INVENTORY_AMOUNT,
              TC_ACTUALQTY, TC_BILLEDQTY, TC_GODOWNNAME, TC_BATCHNAME,
              TC_TRACKINGNUMBER, TC_BATCH_AMOUNT, TC_SALES_LEDGERNAME,
              TC_SALES_ISDEEMEDPOSITIVE, TC_SALES_AMOUNT
            </FIELDS>
            <XMLTAG>ALLINVENTORYENTRIES.LIST</XMLTAG>
          </LINE>

          <LINE NAME="TC_INVOICEORDER">
            <FIELDS>TC_BASICORDERDATE, TC_BASICPURCHASEORDERNO</FIELDS>
            <XMLTAG>INVOICEORDERLIST.LIST</XMLTAG>
          </LINE>

            <LINE NAME="TC_ADDRESSES">
                <FIELDS>TC_ADDRESS</FIELDS>
                <XMLTAG>ADDRESS.LIST</XMLTAG>
                <XMLTYPE>String</XMLTYPE>
            </LINE>

          <!-- Fields -->
          <FIELD NAME="TC_GUID"><SET>$GUID</SET><XMLTAG>GUID</XMLTAG></FIELD>
          <FIELD NAME="TC_ALTERID"><SET>$AlterID</SET><XMLTAG>ALTERID</XMLTAG></FIELD>
          <FIELD NAME="TC_MASTERID"><SET>$MasterID</SET><XMLTAG>MASTERID</XMLTAG></FIELD>
          <FIELD NAME="TC_DATE"><SET>$Date</SET><XMLTAG>DATE</XMLTAG></FIELD>
          <FIELD NAME="TC_VOUCHERTYPE"><SET>$VoucherTypeName</SET><XMLTAG>VOUCHERTYPENAME</XMLTAG></FIELD>
          <FIELD NAME="TC_VOUCHERNUMBER"><SET>$VoucherNumber</SET><XMLTAG>VOUCHERNUMBER</XMLTAG></FIELD>
          <FIELD NAME="TC_PARTYLEDGERNAME"><SET>$PartyLedgerName</SET><XMLTAG>PARTYLEDGERNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICBUYERNAME"><SET>$BasicBuyerName</SET><XMLTAG>BASICBUYERNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICBASEPARTYNAME"><SET>$BasicBasePartyName</SET><XMLTAG>BASICBASEPARTYNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_PARTYGSTIN"><SET>$PartyGSTIN</SET><XMLTAG>PARTYGSTIN</XMLTAG></FIELD>
          <FIELD NAME="TC_PLACEOFSUPPLY"><SET>$PlaceOfSupply</SET><XMLTAG>PLACEOFSUPPLY</XMLTAG></FIELD>
          <FIELD NAME="TC_CONSIGNEEMAILINGNAME"><SET>$ConsigneeMailingName</SET><XMLTAG>CONSIGNEEMAILINGNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_CONSIGNEESTATENAME"><SET>$ConsigneeStateName</SET><XMLTAG>CONSIGNEESTATENAME</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICSHIPDOCUMENTNO"><SET>$BasicShipDocumentNo</SET><XMLTAG>BASICSHIPDOCUMENTNO</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICFINALDESTINATION"><SET>$BasicFinalDestination</SET><XMLTAG>BASICFINALDESTINATION</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICDUEDATEOFPYMT"><SET>$BasicDueDateOfPymt</SET><XMLTAG>BASICDUEDATEOFPYMT</XMLTAG></FIELD>
          <FIELD NAME="TC_CONSIGNEECOUNTRYNAME"><SET>$ConsigneeCountryName</SET><XMLTAG>CONSIGNEECOUNTRYNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_REFERENCE"><SET>$Reference</SET><XMLTAG>REFERENCE</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICSHIPPEDBY"><SET>$BasicShippedBy</SET><XMLTAG>BASICSHIPPEDBY</XMLTAG></FIELD>

          <FIELD NAME="TC_LEDGERNAME"><SET>$LedgerName</SET><XMLTAG>LEDGERNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_AMOUNT"><SET>$Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>

          <!-- Inventory Fields -->
          <FIELD NAME="TC_STOCKITEMNAME"><SET>$StockItemName</SET><XMLTAG>STOCKITEMNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_ISDEEMEDPOSITIVE"><SET>$IsDeemedPositive</SET><XMLTAG>ISDEEMEDPOSITIVE</XMLTAG></FIELD>
          <FIELD NAME="TC_RATE"><SET>$Rate</SET><XMLTAG>RATE</XMLTAG></FIELD>
          <FIELD NAME="TC_INVENTORY_AMOUNT"><SET>$Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>
          <FIELD NAME="TC_ACTUALQTY"><SET>$ActualQty</SET><XMLTAG>ACTUALQTY</XMLTAG></FIELD>
          <FIELD NAME="TC_BILLEDQTY"><SET>$BilledQty</SET><XMLTAG>BILLEDQTY</XMLTAG></FIELD>
          <FIELD NAME="TC_GODOWNNAME"><SET>$BatchAllocations.GodownName</SET><XMLTAG>GODOWNNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_BATCHNAME"><SET>$BatchAllocations.BatchName</SET><XMLTAG>BATCHNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_TRACKINGNUMBER"><SET>$BatchAllocations.TrackingNumber</SET><XMLTAG>TRACKINGNUMBER</XMLTAG></FIELD>
          <FIELD NAME="TC_BATCH_AMOUNT"><SET>$BatchAllocations.Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>
          <FIELD NAME="TC_SALES_LEDGERNAME"><SET>$AccountingAllocations.LedgerName</SET><XMLTAG>LEDGERNAME</XMLTAG></FIELD>
          <FIELD NAME="TC_SALES_ISDEEMEDPOSITIVE"><SET>$AccountingAllocations.IsDeemedPositive</SET><XMLTAG>ISDEEMEDPOSITIVE</XMLTAG></FIELD>
          <FIELD NAME="TC_SALES_AMOUNT"><SET>$AccountingAllocations.Amount</SET><XMLTAG>AMOUNT</XMLTAG></FIELD>

          <!-- Invoice Order Fields -->

          <FIELD NAME="TC_BASICORDERDATE"><SET>$BasicOrderDate</SET><XMLTAG>ORDERDATE</XMLTAG></FIELD>
          <FIELD NAME="TC_BASICPURCHASEORDERNO"><SET>$BasicPurchaseOrderNo</SET><XMLTAG>PURCHASEORDERNO</XMLTAG></FIELD>  
          <!-- Address Fields -->
          <FIELD NAME="TC_ADDRESS"><SET>$Address</SET><XMLTAG>ADDRESS</XMLTAG></FIELD>

          <!-- Collection -->
          <COLLECTION NAME="TC_VOUCHERCOLLECTION">
            <TYPE>Vouchers</TYPE>
            <FILTERS>IsSalesVoucher</FILTERS>
            <NATIVEMETHOD>ALTERID</NATIVEMETHOD>
            <NATIVEMETHOD>ALLLEDGERENTRIES.LEDGERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>PARTYLEDGERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICBUYERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICBASEPARTYNAME</NATIVEMETHOD>
            <NATIVEMETHOD>PARTYGSTIN</NATIVEMETHOD>
            <NATIVEMETHOD>PLACEOFSUPPLY</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEEMAILINGNAME</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEESTATENAME</NATIVEMETHOD>
            <NATIVEMETHOD>BASICSHIPDOCUMENTNO</NATIVEMETHOD>
            <NATIVEMETHOD>BASICFINALDESTINATION</NATIVEMETHOD>
            <NATIVEMETHOD>BASICDUEDATEOFPYMT</NATIVEMETHOD>
            <NATIVEMETHOD>CONSIGNEECOUNTRYNAME</NATIVEMETHOD>
            <NATIVEMETHOD>REFERENCE</NATIVEMETHOD>
            <NATIVEMETHOD>BASICSHIPPEDBY</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.STOCKITEMNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACTUALQTY</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BILLEDQTY</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.RATE</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.AMOUNT</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.GODOWNNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.BATCHNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.TRACKINGNUMBER</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.BATCHALLOCATIONS.AMOUNT</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACCOUNTINGALLOCATIONS.LEDGERNAME</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACCOUNTINGALLOCATIONS.AMOUNT</NATIVEMETHOD>
            <NATIVEMETHOD>ALLINVENTORYENTRIES.ACCOUNTINGALLOCATIONS.ISDEEMEDPOSITIVE</NATIVEMETHOD>
            <NATIVEMETHOD>INVOICEORDERLIST.BASICORDERDATE</NATIVEMETHOD>
            <NATIVEMETHOD>INVOICEORDERLIST.BASICPURCHASEORDERNO</NATIVEMETHOD>
            <NATIVEMETHOD>ADDRESS</NATIVEMETHOD>

          </COLLECTION>

        </TDLMESSAGE>
      </TDL>
    </DESC>
  </BODY>
</ENVELOPE>
